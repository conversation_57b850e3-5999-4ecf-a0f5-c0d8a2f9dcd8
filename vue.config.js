const packageName = process.env.VUE_APP_PACKAGE_NAME || 'HybridApp';
const HtmlWebpackPlugin = require('html-webpack-plugin');
const path = require('path');
const BundleAnalyzerPlugin =
  require('webpack-bundle-analyzer').BundleAnalyzerPlugin;

module.exports = {
  productionSourceMap: false,
  publicPath: process.env.VUE_APP_PUBLIC_PATH || '/',
  outputDir: packageName,
  configureWebpack: {
    externals: {
      moment: 'moment',
    },
    plugins: [
      new HtmlWebpackPlugin({
        favicon: path.resolve(__dirname, './public/favicon.ico'),
        title: '远程银行',
        template: path.resolve(__dirname, './public/index.html'),
      }),
      new BundleAnalyzerPlugin({
        analyzerMode:
          process.env.NODE_ENV === 'production' ? 'static' : 'disabled',
        generateStatsFile: true,
        openAnalyzer: false,
      }),
    ],
    optimization: {
      splitChunks: {
        chunks: 'all',
        maxSize: 512 * 1024, // 244KB
        cacheGroups: {
          vendors: {
            test: /[\\/]node_modules[\\/]/,
            priority: -10,
            reuseExistingChunk: true,
          },
          default: {
            minChunks: 2,
            priority: -20,
            reuseExistingChunk: true,
          },
        },
      },
    },
  },

  devServer: {
    proxy: {
      '/api': {
        target: 'https://testopen.shrbank.com:89/ycyh3/',
        changeOrigin: true,
        pathRewrite: {
          '^/api': '',
        },
      },
    },
  },
};
