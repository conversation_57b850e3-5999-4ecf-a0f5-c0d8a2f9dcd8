import Vue from 'vue';
import VueRouter from 'vue-router';
Vue.use(VueRouter);

const routes = [
  {
    path: '/',
    name: 'Index',
    component: () => import(/* webpackChunkName: "Home" */ '@/views/Home.vue'),
  },

  {
    path: '/Home',
    name: 'Home',
    component: () => import(/* webpackChunkName: "Home" */ '@/views/Home.vue'),
  },
  {
    path: '/VideoConnect',
    name: 'VideoConnect',
    component: () =>
      import(/* webpackChunkName: "about" */ '@/views/VideoConnect.vue'),
  },

  {
    path: '/PageContainer',
    name: 'PageContainer',

    component: () =>
      import(
        /* webpackChunkName: "PageContainer" */ '@/views/PageContainer.vue'
      ),
    props: true,
  },

  {
    path: '/Finish',
    name: 'Finish',

    component: () =>
      import(/* webpackChunkName: "Finish" */ '@/views/Finish.vue'),
    props: true,
  },
];

const router = new VueRouter({
  base: process.env.BASE_URL,
  routes,
});

export default router;
