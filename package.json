{"name": "webrtc-demo", "version": "0.2.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "mock": "vue-cli-service serve --mode mock", "build": "vue-cli-service build", "build:n": "vue-cli-service build --mode n", "build:poc": "vue-cli-service build --mode poc", "p": "prettier --write ."}, "dependencies": {"axios": "^0.27.2", "compression-webpack-plugin": "6.1.1", "core-js": "^2.6.9", "element-ui": "^2.15.9", "miniprogram-sm-crypto": "^0.3.13", "moment": "^2.29.4", "vant": "^2.12.54", "vue": "^2.6.11", "vue-router": "^3.2.0", "vuex": "^3.4.0"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.15", "@vue/cli-plugin-eslint": "~4.5.15", "@vue/cli-plugin-router": "~4.5.15", "@vue/cli-plugin-vuex": "~4.5.15", "@vue/cli-service": "~4.5.15", "archiver": "^5.3.1", "babel-eslint": "^10.1.0", "chalk": "^5.2.0", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "ora": "^6.1.2", "prettier": "^2.8.3", "vue-template-compiler": "^2.6.11"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}