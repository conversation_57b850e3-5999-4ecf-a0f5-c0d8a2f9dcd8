/**
 * WebRTC管理器 - 重新设计的WebRTC封装
 * 
 * 设计原则：
 * 1. 单一职责原则 - 每个类只负责一个功能
 * 2. 依赖注入 - 通过构造函数注入依赖
 * 3. 事件驱动 - 使用观察者模式处理事件
 * 4. 状态管理 - 集中管理状态
 * 5. 错误处理 - 统一的错误处理机制
 * 6. 可测试性 - 易于单元测试
 */

/**
 * WebRTC状态管理器
 */
class WebRTCStateManager {
  constructor() {
    this.state = {
      initialized: false,
      connected: false,
      inRoom: false,
      userId: null,
      roomId: null,
      userFlag: null,
      videoParams: {},
      evaluateInfo: {},
      deviceStatus: {
        camera: false,
        microphone: false,
        speaker: false
      }
    };
    this.listeners = new Map();
  }

  setState(newState) {
    const oldState = { ...this.state };
    this.state = { ...this.state, ...newState };
    this.notifyStateChange(oldState, this.state);
  }

  getState() {
    return { ...this.state };
  }

  subscribe(listener) {
    const id = Symbol('listener');
    this.listeners.set(id, listener);
    return () => this.listeners.delete(id);
  }

  notifyStateChange(oldState, newState) {
    this.listeners.forEach(listener => {
      try {
        listener(newState, oldState);
      } catch (error) {
        console.error('State listener error:', error);
      }
    });
  }

  reset() {
    this.setState({
      initialized: false,
      connected: false,
      inRoom: false,
      userId: null,
      roomId: null,
      userFlag: null,
      videoParams: {},
      evaluateInfo: {},
      deviceStatus: {
        camera: false,
        microphone: false,
        speaker: false
      }
    });
  }
}

/**
 * 事件管理器
 */
class EventManager {
  constructor() {
    this.events = new Map();
    this.onceEvents = new Map();
  }

  on(eventName, callback) {
    if (!this.events.has(eventName)) {
      this.events.set(eventName, new Set());
    }
    this.events.get(eventName).add(callback);
    return () => this.off(eventName, callback);
  }

  once(eventName, callback) {
    const wrappedCallback = (...args) => {
      callback(...args);
      this.off(eventName, wrappedCallback);
    };
    return this.on(eventName, wrappedCallback);
  }

  off(eventName, callback) {
    if (this.events.has(eventName)) {
      this.events.get(eventName).delete(callback);
      if (this.events.get(eventName).size === 0) {
        this.events.delete(eventName);
      }
    }
  }

  emit(eventName, ...args) {
    if (this.events.has(eventName)) {
      this.events.get(eventName).forEach(callback => {
        try {
          callback(...args);
        } catch (error) {
          console.error(`Event handler error for ${eventName}:`, error);
        }
      });
    }
  }

  removeAllListeners(eventName) {
    if (eventName) {
      this.events.delete(eventName);
    } else {
      this.events.clear();
    }
  }

  listenerCount(eventName) {
    return this.events.has(eventName) ? this.events.get(eventName).size : 0;
  }
}

/**
 * 错误管理器
 */
class ErrorManager {
  constructor(eventManager) {
    this.eventManager = eventManager;
    this.errorHandlers = new Map();
  }

  registerErrorHandler(errorType, handler) {
    if (!this.errorHandlers.has(errorType)) {
      this.errorHandlers.set(errorType, new Set());
    }
    this.errorHandlers.get(errorType).add(handler);
  }

  handleError(error, context = {}) {
    const errorInfo = {
      error,
      context,
      timestamp: new Date().toISOString(),
      type: error.name || 'UnknownError'
    };

    console.error('WebRTC Error:', errorInfo);

    // 触发通用错误事件
    this.eventManager.emit('error', errorInfo);

    // 触发特定类型的错误处理器
    if (this.errorHandlers.has(errorInfo.type)) {
      this.errorHandlers.get(errorInfo.type).forEach(handler => {
        try {
          handler(errorInfo);
        } catch (handlerError) {
          console.error('Error handler failed:', handlerError);
        }
      });
    }

    return errorInfo;
  }

  createErrorHandler(context) {
    return (error) => this.handleError(error, context);
  }
}

/**
 * 设备管理器
 */
class DeviceManager {
  constructor(sdk, stateManager, errorManager) {
    this.sdk = sdk;
    this.stateManager = stateManager;
    this.errorManager = errorManager;
  }

  async checkCameraDevice() {
    try {
      const result = await this.sdk.checkCameraDevice();
      this.stateManager.setState({
        deviceStatus: {
          ...this.stateManager.getState().deviceStatus,
          camera: result
        }
      });
      return result;
    } catch (error) {
      this.errorManager.handleError(error, { method: 'checkCameraDevice' });
      return false;
    }
  }

  async initializeDevices() {
    try {
      const result = await this.sdk.deviceDialogInit();
      return result;
    } catch (error) {
      this.errorManager.handleError(error, { method: 'initializeDevices' });
      throw error;
    }
  }

  async switchCamera(facingMode = 'user') {
    try {
      await this.sdk.switchCamera(facingMode);
    } catch (error) {
      this.errorManager.handleError(error, { method: 'switchCamera', facingMode });
      throw error;
    }
  }

  async muteLocalVideo(mute) {
    try {
      await this.sdk.muteLocalVideo(mute);
    } catch (error) {
      this.errorManager.handleError(error, { method: 'muteLocalVideo', mute });
      throw error;
    }
  }

  async muteLocalAudio(mute) {
    try {
      await this.sdk.muteLocalAudio(mute);
    } catch (error) {
      this.errorManager.handleError(error, { method: 'muteLocalAudio', mute });
      throw error;
    }
  }

  async muteRemoteAudio(userId, mute) {
    try {
      await this.sdk.muteRemoteAudio({ userId, mute });
    } catch (error) {
      this.errorManager.handleError(error, { method: 'muteRemoteAudio', userId, mute });
      throw error;
    }
  }
}

/**
 * 房间管理器
 */
class RoomManager {
  constructor(sdk, stateManager, eventManager, errorManager) {
    this.sdk = sdk;
    this.stateManager = stateManager;
    this.eventManager = eventManager;
    this.errorManager = errorManager;
  }

  async enterRoom(roomId, options = {}) {
    try {
      const result = await this.sdk.enterRoom(Number(roomId));
      this.stateManager.setState({
        inRoom: true,
        roomId: roomId
      });
      this.eventManager.emit('roomEntered', { roomId, result });
      return result;
    } catch (error) {
      this.errorManager.handleError(error, { method: 'enterRoom', roomId });
      throw error;
    }
  }

  async leaveRoom() {
    try {
      const result = await this.sdk.leaveRoom();
      this.stateManager.setState({
        inRoom: false,
        roomId: null
      });
      this.eventManager.emit('roomLeft', { result });
      return result;
    } catch (error) {
      this.errorManager.handleError(error, { method: 'leaveRoom' });
      throw error;
    }
  }

  async playLocalMedia(viewId) {
    try {
      await this.sdk.playLocalMedia({ view: viewId });
      this.eventManager.emit('localMediaStarted', { viewId });
    } catch (error) {
      this.errorManager.handleError(error, { method: 'playLocalMedia', viewId });
      throw error;
    }
  }

  async playRemoteVideo(viewId, userId) {
    try {
      await this.sdk.playRemoteVideo({ view: viewId, userId });
      this.eventManager.emit('remoteVideoStarted', { viewId, userId });
    } catch (error) {
      this.errorManager.handleError(error, { method: 'playRemoteVideo', viewId, userId });
      throw error;
    }
  }

  async stopRemoteVideo(userId) {
    try {
      await this.sdk.stopRemoteVideo({ userId });
      this.eventManager.emit('remoteVideoStopped', { userId });
    } catch (error) {
      this.errorManager.handleError(error, { method: 'stopRemoteVideo', userId });
      throw error;
    }
  }
}

export {
  WebRTCStateManager,
  EventManager,
  ErrorManager,
  DeviceManager,
  RoomManager
};
