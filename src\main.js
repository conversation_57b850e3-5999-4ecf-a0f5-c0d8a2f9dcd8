import Vue from 'vue';
import App from './App.vue';
import router from './router';
import store from './store';

// 按需引入ElementUI组件
import { Button, Select, MessageBox } from 'element-ui';
import 'element-ui/lib/theme-chalk/button.css';
import 'element-ui/lib/theme-chalk/select.css';
import 'element-ui/lib/theme-chalk/message-box.css';

// 按需引入Vant组件
import { Button as VantButton, Toast } from 'vant';
import 'vant/lib/button/style';
import 'vant/lib/toast/style';

if (process.env.VUE_APP_VCONSOLE === 'true') {
  import('../public/static/vconsole.min');
}

Vue.config.productionTip = false;

// 注册ElementUI组件
Vue.use(Button);
Vue.use(Select);
Vue.prototype.$msgbox = MessageBox;

// 注册Vant组件
Vue.use(VantButton);
Vue.use(Toast);

new Vue({
  router,
  store,
  render: (h) => h(App),
}).$mount('#app');
