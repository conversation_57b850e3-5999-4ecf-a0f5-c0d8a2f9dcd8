<template>
  <div class="completion-page">
    <!-- loading -->
    <div v-if="status === 9">
      <div>
        <img class="animationg" src="../assets/time.png" alt="">
      </div>
      <div>
        正在获取面核结果，请稍后...
      </div>
    </div>

    <!-- 面核失败 -->
     <div v-else-if="status == 1" class="flexCG">
      <div>
        <img src="../assets/image.png" alt="">
        <p>面核失败，请重试</p>        
      </div>
      <button @click="retry" class="buttonStyle">重试</button>
     </div>

     <!-- 面核成功 -->
      <div v-else-if="status == 0" class="flexCG">
        <div>
          <img src="../assets/image.png" alt="">
          <p>恭喜您，面核成功</p>        
        </div>
        <button class="buttonStyle" @click="handleReturn">返回首页</button>
      </div>


  </div>
</template>

<script>
import store from '@/store'

export default {
  name: 'CompletionPage',
  data(){
    return {
      currentYear: new Date().getFullYear(),
      status: 9,
    }
  },
  mounted() {
    this.init()
  },
  methods:{
    init() {
      // todo 获取面核结果
      setTimeout(() => {
        // 随机
        if(Math.random() > 0.5) {
          this.status = 1
        } else {
          this.status = 0
        }
      }, 3000)
    },
    handleReturn (){
       this.$router.push({
        name: "Home"
      });
    },
    retry() {
      let query = this.$route.query.message ? this.$route.query : store.state.query;     this.$router.push({
        name: "Home",
        query: {...query}
      });
    }
  }
}
</script>

<style scoped>
.flexCG {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 100%;
}
.animationg {
  width: 100px;
  height: 100px;
  animation: spin 2s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.buttonStyle {
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
  color: #fff;
  border: 0;
  height: 48px;
  background: #e1b77e;
  color: #fff;
  border-radius: 24px;
  width: 90%;
}
.buttonStyle:disabled {
  background: #ecd7b9;
  cursor: not-allowed;
  opacity: 0.7;
}
.completion-page {
  height: 100vh;
  width: 100vw;
  padding: 20px;
  display: flex;
  font-size: 20px;
  font-weight: 600;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.completion-container {
  width: 100%;
  height:30%;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: space-between;
  justify-content: space-around;
}

.completion-icon {
  font-size: 60px;
  color: #52c41a;
  margin-bottom: 20px;
  font-weight: bold;
}

.completion-title {
  color: #1a1a1a;
  font-size: 22px;
  margin-bottom: 15px;
  font-weight: 500;
}


.completion-btn {
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 12px 24px;
  font-size: 16px;
  cursor: pointer;
  width: 100%;
  transition: background-color 0.3s;
}

.completion-btn:hover {
  background-color: #40a9ff;
}

.completion-footer {
  margin-top: 30px;
  font-size: 12px;
  color: #999;
}

/* 移动端适配 */
@media (max-width: 480px) {
  .completion-container {
    padding: 20px;
  }
  
  .completion-icon {
    font-size: 50px;
  }
  
  .completion-title {
    font-size: 20px;
  }
}
</style>